<%- include('utils/title-content', { home: "home" }) %>

<!-- Hidden fields for JavaScript -->
<input type="hidden" id="user-id" value="<%= user ? user.id : '' %>">
<input type="hidden" id="project-id" value="<%= typeof projectId !== 'undefined' ? projectId : '' %>">
<input type="hidden" id="language" value="<%= lng || 'en' %>">

<script src="/js/searchable-dropdown.js"></script>
<script src="/js/archeogrid.js"></script>
<script src="/js/lib/imagesloaded.pkgd.min.js"></script>
<script src="/js/lib/masonry.pkgd.min.js"></script>
<script src="/js/mobile-vitrine.js"></script>
<script src="/js/selection-dropdown.js"></script>
<script src="/js/lazy-loading.js"></script>
<script src="/js/size-sliders.js"></script>
<script src="/js/folders-tree-vitrine.js"></script>
<script src="/js/thesaurus.js"></script>
<script src="/js/search.js"></script>

<link rel="stylesheet" href="/css/mobile-vitrine.css">
<link rel="stylesheet" href="/css/selection-dropdown.css">
<link rel="stylesheet" href="/css/size-sliders.css">
<link rel="stylesheet" href="/css/folders-tree-vitrine.css">
<link rel="stylesheet" href="/css/thesaurus-vitrine.css">

<div class="d-flex justify-content-between" style="height: 79vh;" id="GROS">

    <div id="menuGauche" class="hide-on-mobile d-flex flex-column justify-content-between col-3"
         style="max-height: none; overflow: visible; width: calc(25% + 20px);">

        <div id="left-top" class="d-flex flex-column gap-2">
            <!-- Navigation Links -->
            <div class="container">
                <a href="/thesaurusPactolsV/<%= root %>,<%= projectId %>" class="btn btn-outline-primary btn-sm mb-2">
                    <%=__('browsePACTOLS')%>
                </a>
            </div>

            <!-- Thesauri List -->
            <% if ((typeof info !== 'undefined' && info === 'yes') || (typeof infothesaurus !== 'undefined' && infothesaurus === 'yes')) { %>
                <div class="container">
                    <h2 class="mb-2"><%=__('browseThesauri')%></h2>
                    <ul class="list-group list-group-flush justify-content-end" style="display: inline-block;">
                        <% if (typeof thesaurus !== 'undefined') { %>
                            <% for (const t of thesaurus) { %>
                                <% if (t.visible !== '0') { %>
                                    <% if (t.thesaurus === 'Periodo') { %>
                                        <a href="#" id="<%= t.thesaurus %>" onclick="console.log('Clicking on Periodo'); getThesaurusTreePeriodo('<%= root %>');">
                                            <i class="far fa-clock fa-2x me-1" aria-hidden="true"></i><%= __('period') %>
                                            <span class="badge badge-pill ml-auto" style="color: black;"><%='('+t.nb_tot_item+')'%></span>
                                        </a>
                                    <% } else { %>
                                        <a class="list-group-item list-group-item-action MainPageThesaurus" href="#" id="<%= t.thesaurus %>"
                                           onclick="loadAndDisplayThesaurusTree('<%= root %>','<%= projectId %>', '<%= t.thesaurus %>','<%= t.id_thes %>','multi');">
                                            <% if (t.thesaurus !== 'nd_th13') { %>
                                                <% if (t.thesaurus === 'lrmh') { %>
                                                    <% if (lng === 'fr') { %>
                                                        <img src="/assets/images/lrmh.png" style="height: 2em;">Collection <%= t.name %>
                                                    <% } else { %>
                                                        <img src="/assets/images/lrmh.png" style="height: 2em;"><%= t.short_name %>collection
                                                    <% } %>
                                                <% } else { %>
                                                    <i class="fa fa-book me-1" aria-hidden="true"></i><%= t.name %>
                                                <% } %>
                                            <% } else if (t.thesaurus === 'nd_th13') { %>
                                                <% if (lng === 'fr') { %>
                                                    <i class="fa fa-university fa-2x me-1" aria-hidden="true"></i><%= t.name %> (complet)
                                                <% } else { %>
                                                    <i class="fa fa-university fa-2x me-1" aria-hidden="true"></i><%= t.label %> (complete)
                                                <% } %>
                                            <% } %>
                                            <span class="badge badge-pill text-dark"><%='('+t.nb_tot_item+')'%></span>
                                        </a>
                                    <% } %>
                                <% } %>
                            <% } %>
                        <% } %>

                        <% if (typeof thesaurusSimple !== 'undefined') { %>
                            <% for (const s of thesaurusSimple) { %>
                                <a class="list-group-item list-group-item-action MainPageThesaurus" href="#" id="<%= s.thesaurus %>"
                                   onclick="loadAndDisplayThesaurusTree('<%= root %>','<%= projectId %>', '<%= s.thesaurus %>','<%= s.id_thes %>','simple');">
                                    <% if (lng === 'fr') { %>
                                        <i class="fa fa-book me-1" aria-hidden="true"></i><%= s.name %>
                                    <% } else { %>
                                        <i class="fa fa-book me-1" aria-hidden="true"></i><%= s.label %>
                                    <% } %>
                                    <span class="badge badge-pill text-dark"><%='('+s.nb_tot_item+')'%></span>
                                </a>
                            <% } %>
                        <% } %>
                    </ul>
                </div>
            <% } %>




        </div>

        <div id="left-bot">
            <div class="row">
                <div class="col-12 d-flex align-items-center justify-content-left">
                    <button id="back-to-thesaurus-btn" onclick="hideThesaurusTree()">
                        <i class="fas fa-arrow-left"></i> <%=__('back')%>
                    </button>

                    <h4 class="mb-2" style="margin-left: 10px;"><%=__('browseThesaurus')%></h4>
                </div>
            </div>

            <!-- Searchable Dropdown for Thesaurus Navigation -->
            <div id="thesaurus-dropdown-container" class="container mb-3" style="display: none;">
                <label for="thesaurus-folder-dropdown" class="form-label"><%=__('search')%></label>
                <select id="thesaurus-folder-dropdown" class="searchable-select form-control" data-simple-mode="false">
                    <option value="search" data-type="search"><%=__('searchAllThesaurus')%></option>
                </select>
            </div>

            <div id="dynamic-thesaurus-tree" style="min-height: 50px;">
            </div>
        </div>
    </div>

    <div class="col-12 col-md-9" id="menuCentre" style="display: none;">
        <% if (locals.flash && locals.flash.ok) { %>
        <div class="m-2 alert alert-success text-capitalize">
            <%= flash.ok %>
        </div>
        <% } %>

        <%- include('explore/exploreVitrine') %>
    </div>

</div>

<!-- Modals for thesaurus trees (when needed) -->
<% if (typeof thesaurus !== 'undefined') { %>
    <% for (let d = 0; d < thesaurus.length; d++) { %>
        <% if (thesaurus[d].visible !== '0' && thesaurus[d].name && thesaurus[d].name.indexOf('[') === -1) { %>
        <div class="modal" id="modal_thesaurusTree_<%= thesaurus[d].id || thesaurus[d].id_thes %>" role="dialog">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h5 class="modal-title"><%= thesaurus[d].name || thesaurus[d].label %></h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div id="modal-tree-<%= thesaurus[d].id || thesaurus[d].id_thes %>" style="max-height: 60vh; overflow-y: auto;">
                            <div class="text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden"><%=__('loading')%>...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <% } %>
    <% } %>
<% } %>

<!-- Scripts -->
<script src="/js/explore.js"></script>
<script src="/js/selectionVitrine.js"></script>

<!-- Toast container for notifications -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="thesaurus-toast" class="toast align-items-center text-bg-success border-0" role="alert"
         aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body"></div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"
                    aria-label="Close"></button>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const toastElement = document.getElementById("thesaurus-toast");
        if (toastElement && typeof bootstrap !== 'undefined') {
            bootstrap.Toast.getOrCreateInstance(toastElement);
        }

        // Initialize toast variables for selection functionality
        // Wait a bit to ensure DOM is fully loaded
        setTimeout(function() {
            const toastElement = document.getElementById("thesaurus-toast");
            if (toastElement) {
                window.toastContent = toastElement.querySelector(".toast-body");
                window.projectToast = bootstrap.Toast.getOrCreateInstance(toastElement);
            }
        }, 100);

        $('.modal').on('show.bs.modal', function (event) {
            const modal = $(this);
            const modalId = modal.attr('id');
            const thesaurusId = modalId.replace('modal_thesaurusTree_', '');

            const treeContainer = modal.find('[id^="modal-tree-"]');
            if (treeContainer.length && treeContainer.html().indexOf('spinner-border') > -1) {
                setTimeout(function() {
                    treeContainer.html('<p>Tree structure would be loaded here for thesaurus: ' + thesaurusId + '</p>');
                }, 500);
            }
        });
    });
</script>

<!-- Standalone global functions -->
<script>
    if (!window.loadAndDisplayThesaurusTree) {
        window.loadAndDisplayThesaurusTree = function(root, projectId, thesaurus, id_thes, type) {
            const menuCentre = document.getElementById('menuCentre');
            const exploreDiv = document.getElementById('explore-div');
            // Keep menuCentre hidden by default - only show when user clicks on a folder
            if (menuCentre) menuCentre.style.display = 'none';
            if (exploreDiv) exploreDiv.style.cssText = 'display: none !important;';

            setTimeout(function() {
                if (window.loadAndDisplayThesaurusTreeFull) {
                    window.loadAndDisplayThesaurusTreeFull(root, projectId, thesaurus, id_thes, type);
                }
            }, 100);
        };
    }

    if (!window.getThesaurusTreeMulti) {
        window.getThesaurusTreeMulti = function(root, projectId, thesaurus, id_thes, userId) {
            window.loadAndDisplayThesaurusTree(root, projectId, thesaurus, id_thes, 'multi');
        };
    }

    if (!window.getThesaurusTree) {
        window.getThesaurusTree = function(root, projectId, thesaurus, id_thes, userId) {
            window.loadAndDisplayThesaurusTree(root, projectId, thesaurus, id_thes, 'simple');
        };
    }

    if (!window.getThesaurusTreePeriodo) {
        window.getThesaurusTreePeriodo = function(root) {
            const menuCentre = document.getElementById('menuCentre');
            if (menuCentre) menuCentre.style.display = 'block';
            const exploreResults = document.getElementById('explore-results');
            if (exploreResults) {
                exploreResults.innerHTML = '<div class="alert alert-info">Periodo thesaurus functionality coming soon...</div>';
            }
        };
    }
</script>

<!-- Server-side variables -->
<script>
    // Make projectId available globally (consistent with projectVitrine.ejs)
    window.projectId = "<%= typeof projectId !== 'undefined' ? projectId : '' %>";

    window.SERVER_DATA = {
        USER_READ: <%- JSON.stringify((user && user.read) ? user.read : []) %>,
        USER_STATUS: '<%= (user && user.user_status) ? user.user_status : "guest" %>',
        LNG: '<%= lng || "en" %>',
        USER_WRITE: <%- JSON.stringify((user && user.write) ? user.write : []) %>,
        PROJECT_ID: '<%= typeof projectId !== "undefined" ? projectId : "" %>'
    };
</script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof window.createScrollToTopButton === 'function') {
            window.createScrollToTopButton();
            if (typeof window.updateScrollToTopButtonVisibility === 'function') {
                window.updateScrollToTopButtonVisibility();
                window.addEventListener('scroll', window.updateScrollToTopButtonVisibility);
            }
        }

        const resultsDiv = document.getElementById('explore-results');
        if (resultsDiv) {
            resultsDiv.innerHTML = '<div class="alert alert-info">Select a thesaurus from the left menu to explore its content.</div>';
        }

        const dynamicTreeContainer = document.getElementById('dynamic-thesaurus-tree');
        if (dynamicTreeContainer) {
            dynamicTreeContainer.innerHTML = ''; // Always start empty
        }
    });

    function showThesaurusTree() {
        const leftTop = document.getElementById('left-top');
        const leftBot = document.getElementById('left-bot');
        const backButton = document.getElementById('back-to-thesaurus-btn');
        const dynamicTree = document.getElementById('dynamic-thesaurus-tree');

        if (leftTop) leftTop.classList.add('hidden');
        if (leftBot) leftBot.classList.add('expanded');
        if (backButton) backButton.classList.add('show');
        if (dynamicTree) dynamicTree.classList.add('expanded');
    }

    function hideThesaurusTree() {
        const leftTop = document.getElementById('left-top');
        const leftBot = document.getElementById('left-bot');
        const backButton = document.getElementById('back-to-thesaurus-btn');
        const dynamicTree = document.getElementById('dynamic-thesaurus-tree');
        const menuCentre = document.getElementById('menuCentre');
        const dropdownContainer = document.getElementById('thesaurus-dropdown-container');

        if (leftTop) leftTop.classList.remove('hidden');
        if (leftBot) leftBot.classList.remove('expanded');
        if (backButton) backButton.classList.remove('show');
        if (dynamicTree) {
            dynamicTree.classList.remove('expanded');
            dynamicTree.innerHTML = '';
        }
        if (menuCentre) menuCentre.style.display = 'none';
        if (dropdownContainer) {
            dropdownContainer.style.display = 'none';
            // Clear the dropdown options except the search option
            const dropdown = document.getElementById('thesaurus-folder-dropdown');
            if (dropdown) {
                const searchOption = dropdown.querySelector('option[data-type="search"]');
                dropdown.innerHTML = '';
                if (searchOption) {
                    dropdown.appendChild(searchOption);
                }
            }
        }

        // Clear the thesaurus context
        if (window.currentThesaurusContext) {
            window.currentThesaurusContext.thesaurus = null;
            window.currentThesaurusContext.type = null;
        }
    }

    window.loadAndDisplayThesaurusTreeFull = function(root, projectId, thesaurus, id_thes, type) {
        const apiUrl = type === 'multi'
            ? `/getThesaurusTreeDataMultiVitrine/${root},${thesaurus},${id_thes}`
            : `/getThesaurusTreeDataVitrine/${root},${thesaurus},${id_thes}`;

        const menuCentre = document.getElementById('menuCentre');
        const exploreDiv = document.getElementById('explore-div');

        // Keep menuCentre hidden by default - only show when user clicks on a folder
        if (menuCentre) {
            menuCentre.style.display = 'none';
        }

        if (exploreDiv) {
            exploreDiv.style.cssText = 'display: none !important;';
        }

        showThesaurusTree();

        fetch(apiUrl)
            .then(response => response.json())
            .then(data => {
                renderThesaurusTree(data, thesaurus, type);
            })
            .catch(error => {
                console.error('Error loading thesaurus tree:', error);
                const container = document.getElementById('dynamic-thesaurus-tree');
                if (container) {
                    container.innerHTML = '<div class="alert alert-danger">Error loading thesaurus tree</div>';
                }
            });
    };

    window.loadAndDisplayThesaurusTree = window.loadAndDisplayThesaurusTreeFull;

    // Search functionality for thesaurus
    const executeSearch = function(searchTerm) {
        console.log('[THESAURUS SEARCH] executeSearch called with term:', searchTerm);

        let trimmedSearchTerm = searchTerm.trim();

        const selectedFolder = $('.folders-tree li.folder-selected .folder-name');
        let prefix = '';
        if (selectedFolder.length > 0) {
            const folderName = (selectedFolder.attr('data-full-name') || selectedFolder.text() || '').trim();
            if (folderName) {
                prefix = folderName + ': ';
                if (trimmedSearchTerm.includes(prefix)) {
                    trimmedSearchTerm = trimmedSearchTerm.replace(prefix, '').trim();
                }
            }
        }

        console.log('[THESAURUS SEARCH] Processed search term:', trimmedSearchTerm, 'prefix:', prefix);

        const isEffectivelyEmpty = trimmedSearchTerm.length === 0 ||
                                   (prefix && searchTerm.trim() === prefix.trim()) ||
                                   (prefix && searchTerm.trim() === prefix);

        const isCompletelyEmpty = searchTerm.trim() === '';

        console.log('[THESAURUS SEARCH] Search state - isCompletelyEmpty:', isCompletelyEmpty, 'isEffectivelyEmpty:', isEffectivelyEmpty);

        if (isCompletelyEmpty) {
            console.log('[THESAURUS SEARCH] Completely empty - hiding explore-div, keeping menuCentre');
            // Return to default state - hide explore results but keep menuCentre visible if it was shown
            $('#explore-div').hide();
            console.log('[THESAURUS SEARCH] MenuCentre visibility after empty search:', $('#menuCentre').is(':visible'));
            return;
        }

        if (isEffectivelyEmpty) {
            console.log('[THESAURUS SEARCH] Effectively empty - checking for selected folder');
            const selectedFolderLi = $('.folders-tree li.folder-selected');
            if (selectedFolderLi.length > 0 && selectedFolderLi.attr('folderId')) {
                // For thesaurus, use exploreThesaurusItems instead of exploreFolderVitrine
                const folderId = selectedFolderLi.attr('folderId');
                const currentThesaurus = window.currentThesaurusContext ? window.currentThesaurusContext.thesaurus : null;
                const currentType = window.currentThesaurusContext ? window.currentThesaurusContext.type : null;
                console.log('[THESAURUS SEARCH] Calling exploreThesaurusItems with:', { thesaurus: currentThesaurus, folderId, type: currentType });
                if (currentThesaurus && currentType && typeof exploreThesaurusItems === 'function') {
                    exploreThesaurusItems(currentThesaurus, folderId, currentType);
                }
            } else {
                console.log('[THESAURUS SEARCH] No folder selected - hiding explore-div');
                // No folder selected - hide explore results
                $('#explore-div').hide();
            }
            console.log('[THESAURUS SEARCH] MenuCentre visibility after effectively empty:', $('#menuCentre').is(':visible'));
            return;
        }

        if (trimmedSearchTerm.length >= 2) {
            console.log('[THESAURUS SEARCH] Performing actual search with term:', trimmedSearchTerm);
            if (typeof exploreSearch !== 'function') {
                console.error('[THESAURUS SEARCH] exploreSearch function not available!');
                return;
            }
            let searchParams = { all: [[trimmedSearchTerm]] };
            const selectedFolder = $('.folders-tree li.folder-selected');

            console.log('[THESAURUS SEARCH] Selected folder info:', {
                length: selectedFolder.length,
                folderId: selectedFolder.attr('folderId'),
                projectId: window.projectId
            });

            // Match projectVitrine.ejs logic: only add folder if it's selected and not the root project
            if (selectedFolder.length > 0 && selectedFolder.attr('folderId') && selectedFolder.attr('folderId') !== window.projectId) {
                searchParams.folder = selectedFolder.attr('folderId');
                console.log('[THESAURUS SEARCH] Added folder to search params:', selectedFolder.attr('folderId'));
            }

            console.log('[THESAURUS SEARCH] Final search params:', searchParams);
            console.log('[THESAURUS SEARCH] Calling exploreSearch with projectId:', window.projectId);

            exploreSearch(window.projectId, encodeURIComponent(JSON.stringify(searchParams)));

            console.log('[THESAURUS SEARCH] MenuCentre visibility before showing explore-div:', $('#menuCentre').is(':visible'));
            // Keep menuCentre visible during search - don't hide it
            $('#explore-div').css('display', 'flex');
            console.log('[THESAURUS SEARCH] MenuCentre visibility after showing explore-div:', $('#menuCentre').is(':visible'));
        } else {
            console.log('[THESAURUS SEARCH] Search term too short (< 2 chars):', trimmedSearchTerm.length);
        }
    };

    function debounce(func, delay) {
        let timeout;
        return function(...args) {
            const context = this;
            console.log('[THESAURUS SEARCH] Debounce triggered, clearing previous timeout and setting new one with delay:', delay);
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                console.log('[THESAURUS SEARCH] Debounce timeout fired, executing search function');
                func.apply(context, args);
            }, delay);
        };
    }
    const debouncedRealTimeSearch = debounce(executeSearch, 350);

    // Make the debounced search function available globally for thesaurus.js
    window.debouncedRealTimeSearch = function(searchTerm) {
        console.log('[THESAURUS SEARCH] Global debouncedRealTimeSearch called with term:', searchTerm);
        debouncedRealTimeSearch(searchTerm);
    };

    window.performSearch = function() {
        console.log('[THESAURUS SEARCH] performSearch called');
        const searchSelect = document.getElementById('thesaurus-folder-dropdown');
        const customSearchInputContainer = searchSelect.parentElement.querySelector('.custom-select-container');
        const searchInput = customSearchInputContainer ? customSearchInputContainer.querySelector('.custom-select-input') : null;

        console.log('[THESAURUS SEARCH] performSearch elements found:', {
            searchSelect: !!searchSelect,
            customSearchInputContainer: !!customSearchInputContainer,
            searchInput: !!searchInput,
            searchInputValue: searchInput ? searchInput.value : 'N/A'
        });

        if (searchInput && searchInput.value.trim() !== '') {
            const selectedOption = searchSelect.options[searchSelect.selectedIndex];
            console.log('[THESAURUS SEARCH] performSearch with input value:', searchInput.value.trim());
            console.log('[THESAURUS SEARCH] Selected option type:', selectedOption ? selectedOption.dataset.type : 'none');

            if (selectedOption && selectedOption.dataset.type === 'thesaurus-folder') {
                console.log('[THESAURUS SEARCH] Handling thesaurus folder selection:', selectedOption.value);
                // Handle thesaurus folder selection
                if (typeof window.handleThesaurusFolderSelection === 'function') {
                    window.handleThesaurusFolderSelection(selectedOption.value);
                }
            } else {
                console.log('[THESAURUS SEARCH] Executing search from performSearch');
                executeSearch(searchInput.value.trim());
            }
        } else {
            console.log('[THESAURUS SEARCH] performSearch with empty input - checking selected folder');
            const selectedFolderLi = $('.folders-tree li.folder-selected');
            if (selectedFolderLi.length > 0 && selectedFolderLi.attr('folderId')) {
                const folderId = selectedFolderLi.attr('folderId');
                const currentThesaurus = window.currentThesaurusContext ? window.currentThesaurusContext.thesaurus : null;
                const currentType = window.currentThesaurusContext ? window.currentThesaurusContext.type : null;
                console.log('[THESAURUS SEARCH] performSearch calling exploreThesaurusItems:', { thesaurus: currentThesaurus, folderId, type: currentType });
                if (currentThesaurus && currentType && typeof exploreThesaurusItems === 'function') {
                    exploreThesaurusItems(currentThesaurus, folderId, currentType);
                }
            } else {
                console.log('[THESAURUS SEARCH] performSearch - no folder selected, hiding explore-div');
                // No folder selected - hide explore results but keep menuCentre if it was visible
                $('#explore-div').hide();
                console.log('[THESAURUS SEARCH] MenuCentre visibility after performSearch hide:', $('#menuCentre').is(':visible'));
            }
        }
    };

    function revertToDefaultView() {
        const selectedFolderLi = $('.folders-tree li.folder-selected');
        if (selectedFolderLi.length > 0 && selectedFolderLi.attr('folderId')) {
            const folderId = selectedFolderLi.attr('folderId');
            const currentThesaurus = window.currentThesaurusContext ? window.currentThesaurusContext.thesaurus : null;
            const currentType = window.currentThesaurusContext ? window.currentThesaurusContext.type : null;
            if (currentThesaurus && currentType && typeof exploreThesaurusItems === 'function') {
                exploreThesaurusItems(currentThesaurus, folderId, currentType);
            } else {
                // Hide explore results but keep menuCentre if it was visible
                $('#explore-div').hide();
            }
        } else {
            // No folder selected - hide explore results but keep menuCentre if it was visible
            $('#explore-div').hide();
        }
    }

    // Monitor menuCentre visibility changes
    function monitorMenuCentreVisibility() {
        const menuCentre = document.getElementById('menuCentre');
        if (menuCentre) {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                        const isVisible = $(menuCentre).is(':visible');
                        console.log('[THESAURUS SEARCH] MenuCentre visibility changed to:', isVisible, 'style:', menuCentre.style.display);
                        console.trace('[THESAURUS SEARCH] MenuCentre visibility change stack trace');
                    }
                });
            });
            observer.observe(menuCentre, { attributes: true, attributeFilter: ['style'] });
            console.log('[THESAURUS SEARCH] MenuCentre visibility monitor started');
        }
    }

    // Start monitoring when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', monitorMenuCentreVisibility);
    } else {
        monitorMenuCentreVisibility();
    }

    // Note: The search functionality is now handled in thesaurus.js via setupThesaurusDropdownHandler
    // to avoid conflicts and ensure proper integration with the thesaurus tree functionality


</script>